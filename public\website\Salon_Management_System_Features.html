<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIINK - Complete Beauty Center Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            border-radius: 15px;
            margin: -20px -20px 40px -20px;
        }
        
        .logo {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.4em;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .tagline {
            font-size: 1.1em;
            margin-top: 10px;
            opacity: 0.8;
            font-style: italic;
        }
        
        h1 {
            color: #7c486c;
            font-size: 2.8em;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #401B1B;
            font-size: 2em;
            margin-bottom: 25px;
            margin-top: 45px;
            padding-bottom: 15px;
            border-bottom: 3px solid #FBEBEC;
            position: relative;
        }
        
        h2::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: #7c486c;
        }
        
        h3 {
            color: #7c486c;
            font-size: 1.4em;
            margin-bottom: 15px;
            margin-top: 25px;
            font-weight: 600;
        }
        
        h4 {
            color: #401B1B;
            font-size: 1.2em;
            margin-bottom: 12px;
            margin-top: 18px;
            font-weight: 500;
        }
        
        .section {
            margin-bottom: 45px;
            padding: 30px;
            background: #FBEBEC;
            border-radius: 15px;
            border-left: 6px solid #7c486c;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .step {
            margin-bottom: 30px;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
            border: 1px solid #ECD8BD;
        }
        
        .field-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 18px 0;
            border: 1px solid #e9ecef;
        }
        
        .field-item {
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
            color: #495057;
        }
        
        .field-item:last-child {
            border-bottom: none;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-top: 5px solid #7c486c;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .dashboard-card {
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-3px);
        }
        
        .dashboard-card h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 12px;
            font-weight: 600;
        }
        
        .dashboard-card p {
            opacity: 0.9;
            font-size: 1em;
            line-height: 1.4;
        }
        
        .highlight {
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .highlight h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.6em;
        }
        
        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .process-step {
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            padding: 18px 25px;
            border-radius: 30px;
            margin: 8px;
            font-weight: 600;
            text-align: center;
            flex: 1;
            min-width: 160px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .process-step:hover {
            transform: scale(1.05);
        }
        
        .process-arrow {
            font-size: 1.8em;
            color: #7c486c;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .stats-overview {
            background: linear-gradient(135deg, #ECD8BD 0%, #FBEBEC 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #7c486c;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .table-columns {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 18px 0;
            border-left: 5px solid #2196F3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }
        
        .menu-item {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 18px 0;
            border-left: 5px solid #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
        }
        
        .warning {
            background: #fff8e1;
            color: #e65100;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ff9800;
            margin: 18px 0;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #4caf50;
            margin: 18px 0;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
        }
        
        .info {
            background: #e3f2fd;
            color: #1565c0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196f3;
            margin: 18px 0;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header {
                margin: -15px -15px 30px -15px;
                padding: 30px 15px;
            }
            
            .logo {
                font-size: 2.5em;
            }
            
            h1 {
                font-size: 2.2em;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .process-flow {
                flex-direction: column;
            }
            
            .process-arrow {
                transform: rotate(90deg);
                margin: 15px 0;
            }
            
            .section {
                padding: 20px;
            }
            
            .step {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="https://liink.site/website/assets/images/logo.svg" alt="">
            </div>
            <div class="subtitle">Complete Beauty Center Management System</div>
            <div class="tagline">Streamline Your Beauty Business Operations</div>
        </div>

        <h1>🏪 What Your Beauty Center Can Do With LIINK</h1>

        <!-- Workflow Navigation -->
        <div class="highlight">
            <h3>📋 Complete LIINK Workflow Guide</h3>
            <p>Follow this step-by-step workflow to set up and operate your Beauty Center management system efficiently</p>
            <div style="text-align: center; margin-top: 20px;">
                <a href="https://liink.site/website/Salon_Management_System_Workflow.html" style="background: white; color: #7c486c; padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    🚀 View Complete Workflow
                </a>
            </div>
        </div>

        <div class="stats-overview">
            <h2 style="color: #7c486c; margin-top: 0;">📊 Complete Business Management Platform</h2>
            <p style="font-size: 1.2em; margin-bottom: 20px;">From registration to full operations - everything your Beauty Center needs in one powerful system</p>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Subscription Plans</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Core Modules</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Features</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Cloud-Based</div>
                </div>
            </div>
        </div>

        <!-- Registration Process -->
        <div class="section">
            <h2>📝 Getting Started - Registration Process</h2>

            <div class="process-flow">
                <div class="process-step">Visit Homepage</div>
                <div class="process-arrow">→</div>
                <div class="process-step">Choose Package</div>
                <div class="process-arrow">→</div>
                <div class="process-step">Register</div>
                <div class="process-arrow">→</div>
                <div class="process-step">Setup Center</div>
            </div>

            <div class="step">
                <h3>🎯 Package Selection</h3>
                <p>Choose from our flexible pricing plans designed for every business size:</p>
                <div class="feature-grid">
                    <div class="dashboard-card">
                        <h4>🆓 Free Trial</h4>
                        <p>Perfect for testing our system for a trial period</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📅 Monthly Package</h4>
                        <p>Full access with monthly billing flexibility</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>💰 Yearly Package</h4>
                        <p>Best value with annual commitment and savings</p>
                    </div>
                </div>
            </div>

            <div class="step">
                <h3>📋 Registration Form</h3>
                <div class="field-list">
                    <h4>Basic Information:</h4>
                    <div class="field-item">• Shop Name</div>
                    <div class="field-item">• Email Address</div>
                    <div class="field-item">• Phone Number</div>
                    <div class="field-item">• VAT Number</div>
                    <div class="field-item">• VAT Certification</div>
                    <div class="field-item">• VAT Certification Expiry Date</div>
                    <div class="field-item">• Trade Certification</div>
                    <div class="field-item">• Trade Certification Expiry Date</div>
                    <div class="field-item">• Type of Centre</div>
                    <div class="field-item">• Password & Confirm Password</div>
                </div>

                <div class="field-list">
                    <h4>Address Information:</h4>
                    <div class="field-item">• Street Address</div>
                    <div class="field-item">• City</div>
                    <div class="field-item">• State/Province</div>
                    <div class="field-item">• Postal/Zip Code</div>
                    <div class="field-item">• Country</div>
                </div>
                <div class="success">
                    <strong>✅ After Registration:</strong>
                    A welcome email has been sent automatically.<br>
                    If you have registered for the <strong>Free Trial</strong>, access to the dashboard (including Center Settings) is unlocked immediately.<br>
                    However, if you selected a <strong>Monthly</strong> or <strong>Yearly Subscription</strong>, please wait for <strong>Admin Approval</strong> to access your dashboard.
                </div>
            </div>
        </div>

        <!-- Center Settings -->
        <div class="section">
            <h2>⚙️ Center Settings - Your Business Profile</h2>

            <div class="step">
                <h3>🏢 Complete Business Setup</h3>
                <p>Configure your Beauty Center's complete profile and operational settings:</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📸 Visual Identity</h4>
                        <div class="field-item">• Logo Picture</div>
                        <div class="field-item">• Profile Picture</div>
                        <div class="field-item">• Reception Area Picture</div>
                        <div class="field-item">• Service Area Picture</div>
                    </div>

                    <div class="feature-card">
                        <h4>📍 Location & Contact</h4>
                        <div class="field-item">• Address</div>
                        <div class="field-item">• Google Maps Integration</div>
                        <div class="field-item">• City & State</div>
                        <div class="field-item">• Contact Number</div>
                    </div>

                    <div class="feature-card">
                        <h4>📱 Social Media</h4>
                        <div class="field-item">• Snapchat</div>
                        <div class="field-item">• Instagram</div>
                        <div class="field-item">• X (Twitter)</div>
                        <div class="field-item">• WhatsApp</div>
                    </div>

                    <div class="feature-card">
                        <h4>⏰ Operations</h4>
                        <div class="field-item">• Opening & Closing Time</div>
                        <div class="field-item">• Slot Time (with 5-min cleaning buffer)</div>
                        <div class="field-item">• Appointment Type</div>
                        <div class="field-item">• Center Type</div>
                    </div>
                </div>

                <div class="info">
                    <strong>📝 Additional Setup:</strong> Our Team Description, Reception Area Description, Service Area Description
                </div>
            </div>
        </div>

        <!-- Services Management -->
        <div class="section">
            <h2>💅 Services & Categories Management</h2>

            <div class="step">
                <h3>📊 Services Overview</h3>
                <div class="table-columns">
                    <strong>Service Management Table:</strong>
                    <div class="field-item">• Picture</div>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Center</div>
                    <div class="field-item">• Category</div>
                    <div class="field-item">• Price</div>
                    <div class="field-item">• Description</div>
                    <div class="field-item">• Actions (Add/Edit/Delete)</div>
                </div>
            </div>

            <div class="step">
                <h3>➕ Add New Service</h3>
                <div class="field-list">
                    <div class="field-item">• Branch Selection</div>
                    <div class="field-item">• Category Assignment</div>
                    <div class="field-item">• Service Name</div>
                    <div class="field-item">• Service Price</div>
                    <div class="field-item">• Service Description</div>
                    <div class="field-item">• Service Picture</div>
                    <div class="field-item">• Employee Assignment</div>
                </div>
            </div>
        </div>

        <!-- Staff Management -->
        <div class="section">
            <h2>👥 Complete Staff Management</h2>

            <div class="step">
                <h3>👨‍💼 Employee Management</h3>
                <div class="table-columns">
                    <strong>Employee Overview Table:</strong>
                    <div class="field-item">• Actions</div>
                    <div class="field-item">• Image</div>
                    <div class="field-item">• Shop</div>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Contract Expiry Date</div>
                    <div class="field-item">• Services</div>
                    <div class="field-item">• Revenue</div>
                    <div class="field-item">• Appointments</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📝 Employee Details</h4>
                        <div class="field-item">• Branch</div>
                        <div class="field-item">• Type</div>
                        <div class="field-item">• First & Last Name</div>
                        <div class="field-item">• Email & Password</div>
                        <div class="field-item">• Address & Phone</div>
                        <div class="field-item">• Date of Birth</div>
                        <div class="field-item">• Gender</div>
                    </div>

                    <div class="feature-card">
                        <h4>📄 Documentation</h4>
                        <div class="field-item">• Employee ID Attachment</div>
                        <div class="field-item">• Insurance Attachment</div>
                        <div class="field-item">• Health Card Attachment</div>
                        <div class="field-item">• Profile Picture</div>
                        <div class="field-item">• Contract Expiry Date</div>
                    </div>

                    <div class="feature-card">
                        <h4>💼 Work Assignment</h4>
                        <div class="field-item">• Service Category</div>
                        <div class="field-item">• Services Assignment</div>
                        <div class="field-item">• Last Appointment</div>
                        <div class="field-item">• Consumed Products</div>
                    </div>

                    <div class="feature-card">
                        <h4>📈 Performance Tracking</h4>
                        <div class="field-item">• Leaves Management</div>
                        <div class="field-item">• Revenue Tracking</div>
                        <div class="field-item">• Appointment History</div>
                    </div>
                </div>
            </div>

            <div class="step">
                <h3>💰 Cashier Management</h3>
                <div class="table-columns">
                    <strong>Cashier Management Table:</strong>
                    <div class="field-item">• Actions (View/Edit/Delete)</div>
                    <div class="field-item">• Image</div>
                    <div class="field-item">• Center</div>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Address</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Date of Birth</div>
                    <div class="field-item">• Gender</div>
                </div>

                <div class="field-list">
                    <h4>Add New Cashier:</h4>
                    <div class="field-item">• Branch Selection</div>
                    <div class="field-item">• First & Last Name</div>
                    <div class="field-item">• Email & Password</div>
                    <div class="field-item">• Phone Number & Address</div>
                    <div class="field-item">• Date of Birth & Gender</div>
                    <div class="field-item">• Profile Picture</div>
                </div>
            </div>
        </div>

        <!-- Staff Leaves Management -->
        <div class="section">
            <h2>📅 Employee Leaves Management</h2>

            <div class="step">
                <h3>🏖️ Leave Management</h3>
                <div class="table-columns">
                    <strong>Employee Leaves Table:</strong>
                    <div class="field-item">• Employee Name</div>
                    <div class="field-item">• Date From</div>
                    <div class="field-item">• Date To</div>
                    <div class="field-item">• Days Count</div>
                    <div class="field-item">• Description</div>
                    <div class="field-item">• Actions (View/Edit/Delete)</div>
                </div>

                <div class="field-list">
                    <h4>Add Employee Leave:</h4>
                    <div class="field-item">• Select Employee</div>
                    <div class="field-item">• Leave Date Range</div>
                    <div class="field-item">• Leave Description</div>
                </div>
            </div>
        </div>

        <!-- Amenities -->
        <div class="section">
            <h2>🏨 Amenities Management</h2>

            <div class="step">
                <h3>🛋️ Beauty Center Amenities</h3>
                <div class="table-columns">
                    <strong>Amenities Table:</strong>
                    <div class="field-item">• Picture</div>
                    <div class="field-item">• Amenity Name</div>
                    <div class="field-item">• Actions (Delete option)</div>
                </div>

                <div class="field-list">
                    <h4>Add New Amenity:</h4>
                    <div class="field-item">• Select Amenities</div>
                </div>
            </div>
        </div>

        <!-- Business Dashboard -->
        <div class="section">
            <h2>📊 Business Dashboard - Complete Overview</h2>

            <div class="step">
                <h3>📈 Dashboard Analytics</h3>
                <div class="feature-grid">
                    <div class="dashboard-card">
                        <h4>📅 Active Appointments</h4>
                        <p>Current day appointments in progress</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>⏰ Upcoming Appointments</h4>
                        <p>Future scheduled appointments</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📊 Total Appointments</h4>
                        <p>Complete appointment history</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>👥 All Customers</h4>
                        <p>Complete customers count</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>💅 All Services</h4>
                        <p>Complete service catalog</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>🛍️ All Products</h4>
                        <p>Complete product inventory</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>💰 Total Revenue</h4>
                        <p>Complete financial overview</p>
                    </div>
                </div>
            </div>

           <div class="step">
                <h3 style="font-size: 24px; font-weight: bold; margin-bottom: 10px;">
                    📅 Appointments Calendar
                </h3>
                <p style="color: #444; font-size: 16px; margin-bottom: 20px;">
                    Our system provides a visual <strong>Appointments Calendar</strong> where all customer appointments are color-coded based on their status:
                    <br><br>
                    <span style="color: #FFD700;">🟡 Pending</span> |
                    <span style="color: #008000;">🟢 Approved</span> |
                    <span style="color: #0000FF;">🔵 Complete</span> |
                    <span style="color: #FF0000;">🔴 Cancelled</span>
                    <br><br>
                    This helps in quickly identifying and managing daily, weekly, or monthly appointments.
                </p>
                <!-- Screenshot of the calendar -->
                <img 
                    src="https://liink.site/website/calendar.png"
                    alt="Appointments Calendar Screenshot" 
                    style="width: 100%; max-width: 100%; border: 1px solid #ccc; border-radius: 8px;"
                />
            </div>
            <div class="step">
                <h3>📋 Dashboard Tables</h3>
                <div class="table-columns">
                    <strong>Assigned Employee Performance:</strong>
                    <div class="field-item">• Employee Name</div>
                    <div class="field-item">• Assigned Clients</div>
                    <div class="field-item">• Revenue</div>
                    <div class="field-item">• Action (View assigned clients)</div>
                </div>
                <div class="table-columns">
                    <strong>Appointments Table:</strong>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Address</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Time</div>
                    <div class="field-item">• Date</div>
                    <div class="field-item">• Status</div>
                    <div class="field-item">• Client Type</div>
                    <div class="field-item">• Action (View)</div>
                </div>
            </div>
        </div>

        <!-- Branch Management -->
        <div class="section">
            <h2>🏢 Branch Management & Subscriptions</h2>

            <div class="step">
                <h3>🏪 Multi-Branch Operations</h3>
                <p>Manage multiple Beauty Center locations from one central system:</p>

                <div class="table-columns">
                    <strong>Subscription Management:</strong>
                    <div class="field-item">• Price</div>
                    <div class="field-item">• Package Type</div>
                    <div class="field-item">• Center Type</div>
                    <div class="field-item">• Payment Date</div>
                    <div class="field-item">• Next Payment Date</div>
                    <div class="field-item">• Subscription Type</div>
                    <div class="field-item">• Invoice</div>
                    <div class="field-item">• Action (Update Subscription)</div>
                </div>

                <div class="field-list">
                    <h4>Add New Branch:</h4>
                    <div class="field-item">• Branch Details</div>
                    <div class="field-item">• Package Selection (Monthly or Yearly)</div>
                    <div class="field-item">• Payment Completion</div>
                </div>
            </div>
        </div>

        <!-- Inventory Management -->
        <div class="section">
            <h2>📦 Complete Inventory Management</h2>

            <div class="step">
                <h3>🏭 Supplier Management</h3>
                <div class="field-list">
                    <h4>Add Supplier Form:</h4>
                    <div class="field-item">• Supplier Name</div>
                    <div class="field-item">• Contact Information</div>
                    <div class="field-item">• Company Details</div>
                    <div class="field-item">• Category (Consumables or Equipment)</div>
                </div>
            </div>

            <div class="step">
                <h3>📦 Product Management</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🛍️ Consumables</h4>
                        <p>Items sold to customers:</p>
                        <div class="field-item">• Shampoo</div>
                        <div class="field-item">• Hair Dyes</div>
                        <div class="field-item">• Towels</div>
                        <div class="field-item">• Hair Oils</div>
                        <div class="field-item">• Creams & Wax</div>
                        <div class="success">Rotating stock for daily operations</div>
                    </div>

                    <div class="feature-card">
                        <h4>🔧 Equipment</h4>
                        <p>Tools and machines for Beauty Center use:</p>
                        <div class="field-item">• Chairs</div>
                        <div class="field-item">• Scissors</div>
                        <div class="field-item">• Spray Bottles</div>
                        <div class="field-item">• Hair Dryers</div>
                        <div class="field-item">• Machines</div>
                        <div class="info">Used by staff, not sold to customers</div>
                    </div>

                    <div class="feature-card">
                        <h4>🏷️ Product Brands</h4>
                        <p>Brand management for products:</p>
                        <div class="field-item">• Brand Name</div>
                        <div class="field-item">• Brand Description</div>
                        <div class="field-item">• Product Association</div>
                        <div class="info">Organize products by brand</div>
                    </div>
                </div>
            </div>

            <div class="step">
                <h3>📥 Stock-In Process</h3>
                <div class="field-list">
                    <div class="field-item">• Go to "Product Review"</div>
                    <div class="field-item">• Use "Add Stock-In" for each new product quantity</div>
                    <div class="field-item">• Each Stock-in = 1 PO (Purchase Order)</div>
                    <div class="field-item">• PO is auto-created when stock-in is made</div>
                </div>

                <div class="warning">
                    <strong>📊 Product Threshold:</strong> Set minimum stock limits in Product View Page. System alerts when stock falls below threshold.
                </div>
            </div>

            <div class="step">
                <h3>📤 Stock Consumption Logic</h3>
                <div class="info">
                    <strong>🔄 FIFO System:</strong> First In, First Out logic ensures proper stock rotation
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🛠️ Service (Manual)</h4>
                        <p>Manual stock-out when service is performed (e.g., shampoo used for customer)</p>
                    </div>

                    <div class="feature-card">
                        <h4>💰 Sale (Manual/Auto)</h4>
                        <p>Manual: Mark item sold via "Stock-out" page</p>
                        <p>Auto: Product auto-deducted when sale made via invoice</p>
                    </div>

                    <div class="feature-card">
                        <h4>⏰ Expiry (Auto)</h4>
                        <p>System auto-marks stock-out when product expires</p>
                        <div class="warning">Expiring products prioritized first</div>
                    </div>

                    <div class="feature-card">
                        <h4>↩️ Retain (Refund)</h4>
                        <p><b>B2B:</b> Products returned to supplier</p>
                        <p><b>B2C:</b> Customer refunds allowed within 3 days only</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Management -->
        <div class="section">
            <h2>💰 Complete Financial Management</h2>

            <div class="step">
                <h3>💸 Expense Management</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🛒 PO → Cost → Expense</h4>
                        <p>Purchase Order costs automatically recorded in expenses</p>
                    </div>

                    <div class="feature-card">
                        <h4>⏰ Expiry Costs</h4>
                        <p>Expired products recorded as expense cost</p>
                    </div>

                    <div class="feature-card">
                        <h4>✍️ Manual Expenses</h4>
                        <p>Use "Add Expense" form for other business costs</p>
                    </div>
                </div>

                <div class="info">
                    <strong>📅 Note:</strong> Current month is pre-selected in date filters with clear expense categories
                </div>
            </div>

            <div class="step">
                <h3>📈 Revenue Management</h3>
                <div class="feature-grid">
                    <div class="dashboard-card">
                        <h4>🛍️ Sales Revenue</h4>
                        <p>Customer product purchases logged as sales revenue</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>💅 Service Revenue</h4>
                        <p>Customer services logged as service revenue</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📄 Combined Invoices</h4>
                        <p>Sales and Services can appear in single invoice</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>✍️ Manual Revenue</h4>
                        <p>Manual income entry via Add Revenue form</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>🏢 B2B Refunds</h4>
                        <p>Supplier refunds added to Revenue, not Expense</p>
                    </div>
                </div>
            </div>

            <div class="step">
                <h3>📊 Advanced Financial Analytics</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📋 Income Statement</h4>
                        <p>Saudi government compliance ready</p>
                        <div class="field-item">• Exportable for external audits</div>
                    </div>
                    <div class="feature-card">
                        <h4>📋 Employee Statement</h4>
                        <p>Employee performance (Appointments & Products Consumption)</p>
                        <div class="field-item">• Employee Performance Export</div>
                    </div>
                    <div class="feature-card">
                        <h4>📉 Depreciation</h4>
                        <p>Applied to tangible assets (equipment)</p>
                        <div class="field-item">• Calculates per-day cost as item ages</div>
                    </div>

                    <div class="feature-card">
                        <h4>📈 Amortization</h4>
                        <p>Applied to intangible assets or services</p>
                        <div class="field-item">• Shows asset useful life duration</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Evaluations -->
        <div class="section">
            <h2>⭐ Customer Evaluations</h2>

            <div class="step">
                <h3>📝 Evaluation Questions Setting</h3>
                <div class="table-columns">
                    <strong>Evaluation Questions Management:</strong>
                    <div class="field-item">• Create custom evaluation questions</div>
                    <div class="field-item">• Configure rating scales (1-5)</div>
                    <div class="field-item">• Preview evaluation forms</div>
                </div>

                <div class="feature-card">
                    <h4>📊 Question Type Available</h4>
                    <div class="field-item">• Text Feedback Questions</div>
                </div>
            </div>

            <div class="step">
                <h3>⚙️ Customer Evaluations Setting</h3>
                <div class="table-columns">
                    <strong>Customer Evaluations Records:</strong>
                    <div class="field-item">• Center</div>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Date</div>
                    <div class="field-item">• Employee</div>
                    <div class="field-item">• Total Payment</div>
                    <div class="field-item">• Client Type</div>
                    <div class="field-item">• Ratings</div>
                    <div class="field-item">• Review</div>
                    <div class="field-item">• Date</div>
                    <div class="field-item">• Action</div>
                </div>
                <div class="info">
                    <strong>📅 Note:</strong> Customer feedback links are sent via email, or can also be provided by the cashier or company when their appointment is completed.
                </div>
            </div>
        </div>

        <!-- Discount Management -->
        <div class="section">
            <h2>🎯 Discount Management</h2>

            <div class="step">
                <h3>💰 Discount Creation & Management</h3>
                <div class="table-columns">
                    <strong>Discount Features:</strong>
                    <div class="field-item">• Create percentage-based discounts</div>
                    <div class="field-item">• Create fixed amount discounts</div>
                    <div class="field-item">• Set discount validity periods</div>
                    <div class="field-item">• Apply discounts to Appointments</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📊 Discount Tracking</h4>
                        <div class="field-item">• Revenue impact analysis</div>
                        <div class="field-item">• Customer response tracking</div>
                        <div class="field-item">• Usage and remaning discount quantity</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- LIINK Support -->
        <div class="section">
            <h2>🆘 LIINK Support</h2>
            <div class="step">
                <h3>📚 LIINK Instructor (Help Centre)</h3>
                <div class="table-columns">
                    <strong>Help Centre Features:</strong>
                    <div class="field-item">• Comprehensive video tutorials</div>
                    <div class="field-item">• Step-by-step written guides</div>
                    <div class="field-item">• Feature-specific help sections</div>
                    <div class="field-item">• Search functionality for help topics</div>
                    <div class="field-item">• Best practices and tips</div>
                </div>
                <div class="table-columns">
                    <strong>Help Center Access:</strong>
                    <div class="field-item">• # (Serial Number)</div>
                    <div class="field-item">• Title</div>
                    <div class="field-item">• Actions</div>
                </div>

                <div class="info">
                    <strong>👁️ View-Only Access:</strong> Beauty Center users can view help tutorials but cannot add, edit, or delete tutorials
                </div>

                <div class="menu-item">
                    <strong>Available Actions:</strong>
                    <div class="field-item">• View Tutorial (opens video link)</div>
                    <div class="field-item">• Access help documentation</div>
                    <div class="field-item">• Browse available tutorials</div>
                </div>
            </div>

            <div class="step">
                <h3>🎫 LIINK Support Tickets</h3>
                <div class="table-columns">
                    <strong>Support Ticket System:</strong>
                    <div class="field-item">• Submit technical support requests</div>
                    <div class="field-item">• Track ticket status and progress</div>
                    <div class="field-item">• Ticket history and resolution tracking</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>⚡ Support Priorities</h4>
                        <div class="field-item">• Critical (System Down)</div>
                        <div class="field-item">• High (Major Feature Issues)</div>
                        <div class="field-item">• Medium (Minor Issues)</div>
                        <div class="field-item">• Low (General Questions)</div>
                    </div>

                    <div class="feature-card">
                        <h4>📞 Support Channels</h4>
                        <div class="field-item">• Online Support Tickets</div>
                        <div class="field-item">• Email Support</div>
                        <div class="field-item">• Phone Support (Premium)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer & System Management -->
        <div class="section">
            <h2>👥 Customer & System Management</h2>

            <div class="step">
                <h3>👤 Customer Types</h3>
                <div class="table-columns">
                    <strong>Customer Type Features:</strong>
                    <div class="field-item">• Walk-in Customers</div>
                    <div class="field-item">• Online Customers</div>
                </div>
            </div>

            <div class="step">
                <h3>📅 Off Dates Management</h3>
                <div class="table-columns">
                    <strong>Holiday Management:</strong>
                    <div class="field-item">• Set Beauty Center closure dates</div>
                    <div class="field-item">• Holiday scheduling</div>
                    <div class="field-item">• Maintenance days</div>
                    <div class="field-item">• Special event closures</div>
                </div>
            </div>

            <div class="step">
                <h3>📦 Subscription Management</h3>
                <div class="table-columns">
                    <strong>Subscription Features:</strong>
                    <div class="field-item">• View current subscription plan</div>
                    <div class="field-item">• Upgrade/downgrade subscription</div>
                    <div class="field-item">• Track subscription renewal dates</div>
                    <div class="field-item">• Manage payment methods</div>
                    <div class="field-item">• View subscription history</div>
                </div>
            </div>

            <div class="step">
                <h3>⭐ Purchased Premium Addons</h3>
                <div class="table-columns">
                    <strong>Premium Addon Features:</strong>
                    <div class="field-item">• View purchased premium addons</div>
                    <div class="field-item">• Activate/deactivate addon features</div>
                    <div class="field-item">• Track addon usage and benefits</div>
                    <div class="field-item">• Purchase additional addons</div>
                    <div class="field-item">• Access premium-only features</div>
                </div>
            </div>
        </div>

        <!-- Additional Features -->
        <div class="section">
            <h2>🔧 Additional System Features</h2>
            <div class="step">
                <h3>🔔 Notifications & Alerts</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📬 System Notifications</h4>
                        <div class="field-item">• Appointment notifications</div>
                        <div class="field-item">• Payment reminders</div>
                        <div class="field-item">• Stock alerts</div>
                        <div class="field-item">• System updates</div>
                        <div class="field-item">• Employee notifications, etc</div>
                    </div>

                    <div class="feature-card">
                        <h4>📊 Stock Analytics Dashboard</h4>
                        <div class="field-item">• Total Stock In quantity</div>
                        <div class="field-item">• Total Stock Out quantity</div>
                        <div class="field-item">• Total number of suppliers</div>
                        <div class="field-item">• Net Profit calculation</div>
                        <div class="field-item">• Net Margin percentage</div>
                        <div class="field-item">• Date Range Filter</div>
                        <div class="field-item">• Real-time Stats Updates</div>
                        <div class="field-item">• Revenue vs Cost Analysis</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Key Benefits -->
        <div class="highlight">
            <h3>🎯 Why Choose LIINK for Your Beauty Center?</h3>
            <div class="feature-grid" style="margin-top: 20px;">
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: white;">🚀 Complete Solution</h4>
                    <p>Everything from registration to advanced analytics in one platform</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: white;">💡 Smart Automation</h4>
                    <p>FIFO inventory, auto-expiry tracking, automated financial calculations</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: white;">📱 Multi-Platform</h4>
                    <p>Access from anywhere with cloud-based system</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: white;">🏢 Scalable</h4>
                    <p>Grow from single Beauty Center to multi-branch operations</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4 style="color: white;">📊 Data-Driven</h4>
                    <p>Comprehensive analytics and reporting for informed decisions</p>
                </div>
            </div>
        </div>
        <!-- Footer -->
        <div style="text-align: center; margin-top: 50px; padding: 30px; background: #f8f9fa; border-radius: 15px;">
            <h2 style="color: #7c486c; margin-bottom: 20px;">🔗 Ready to Transform Your Beauty Center Business?</h2>
            <p style="font-size: 1.2em; color: #666; margin-bottom: 20px;">
                Join hundreds of successful Beauty Centers already using LIINK to streamline their operations and boost their profits.
            </p>
            <div style="font-size: 1.1em; color: #7c486c; font-weight: 600;">
                Start with our Free Trial today and experience the difference!
            </div>
        </div>
    </div>
</body>
</html>
