<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIINK Admin Panel - Complete Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            margin: -20px -20px 30px -20px;
        }

        .logo {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .login-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #7c486c;
        }

        .login-info h3 {
            color: #7c486c;
            margin-bottom: 15px;
        }

        .credentials {
            background: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            border: 1px solid #e9ecef;
        }

        .nav-menu {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .nav-menu h3 {
            color: #7c486c;
            margin-bottom: 15px;
        }

        .nav-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .nav-link {
            background: white;
            padding: 12px 15px;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            display: block;
        }

        .nav-link:hover {
            background: #7c486c;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #7c486c;
        }

        .section h2 {
            color: #7c486c;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
        }

        .section h3 {
            color: #7c486c;
            margin: 20px 0 15px 0;
            font-size: 1.3em;
        }

        .section h4 {
            color: #555;
            margin: 15px 0 10px 0;
            font-size: 1.1em;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .kpi-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .kpi-item h5 {
            color: #7c486c;
            margin-bottom: 5px;
        }

        .table-columns {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .table-columns h5 {
            color: #7c486c;
            margin-bottom: 10px;
        }

        .column-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
        }

        .column-item {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 5px;
            border-left: 3px solid #7c486c;
            font-size: 0.9em;
        }

        .form-fields {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .form-fields h5 {
            color: #7c486c;
            margin-bottom: 10px;
        }

        .field-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 8px;
        }

        .field-item {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 5px;
            border-left: 3px solid #7c486c;
            font-size: 0.9em;
        }

        .feature-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }

        .feature-box h4 {
            color: #7c486c;
            margin-bottom: 10px;
        }

        .icon {
            margin-right: 10px;
            font-size: 1.2em;
        }

        .highlight {
            background: linear-gradient(135deg, #7c486c, #7c486c);
            color: white;
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 5px solid #f39c12;
        }

        .note strong {
            color: #d68910;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .kpi-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="https://liink.site/website/assets/images/logo.svg" alt="">
            </div>
            <div class="subtitle">Admin Panel - Complete Documentation</div>
        </div>

        <div class="login-info">
            <h3>🔐 Admin Login Credentials</h3>
            <div class="credentials">
                <strong>Email:</strong> <EMAIL><br>
                <strong>Password:</strong> nmdp7788
            </div>
        </div>

        <div class="nav-menu">
            <h3>📋 Quick Navigation</h3>
            <div class="nav-links">
                <a href="#dashboard" class="nav-link">📊 Dashboard</a>
                <a href="#subscribers" class="nav-link">👥 Subscribers</a>
                <a href="#customers" class="nav-link">🛍️ Customers</a>
                <a href="#products" class="nav-link">📦 Product Flow</a>
                <a href="#subscriptions" class="nav-link">💳 Subscription Plans</a>
                <a href="#addons" class="nav-link">⭐ Premium Add-ons</a>
                <a href="#categories" class="nav-link">🏷️ Categories</a>
                <a href="#amenities" class="nav-link">🏨 Amenities</a>
                <a href="#ads" class="nav-link">📢 Ads Management</a>
                <a href="#blogs" class="nav-link">📝 Blog Management</a>
                <a href="#support" class="nav-link">🎧 Support</a>
                <a href="#expenses" class="nav-link">💰 Expenses</a>
                <a href="#brands" class="nav-link">🏪 Product Brands</a>
                <a href="#website" class="nav-link">🌐 Website Management</a>
                <a href="#settings" class="nav-link">⚙️ Settings</a>
            </div>
        </div>

        <div id="dashboard" class="section">
            <h2><span class="icon">📊</span>Dashboard Overview</h2>
            <p>Upon logging in, the Admin is directed to the Dashboard, which displays key performance indicators (KPIs):</p>

            <div class="kpi-grid">
                <div class="kpi-item">
                    <h5>Total Paid Subscriptions</h5>
                    <p>Active paying subscribers</p>
                </div>
                <div class="kpi-item">
                    <h5>Total Registered Service Providers</h5>
                    <p>All registered beauty centers</p>
                </div>
                <div class="kpi-item">
                    <h5>Total Earnings by Service Providers</h5>
                    <p>Revenue generated by providers</p>
                </div>
                <div class="kpi-item">
                    <h5>Active Subscribers</h5>
                    <p>Currently active subscriptions</p>
                </div>
                <div class="kpi-item">
                    <h5>Total Earnings for Current Month</h5>
                    <p>Monthly revenue tracking</p>
                </div>
                <div class="kpi-item">
                    <h5>New Subscriptions This Month</h5>
                    <p>Monthly subscription growth</p>
                </div>
                <div class="kpi-item">
                    <h5>Churned Subscribers This Month</h5>
                    <p>Monthly subscription cancellations</p>
                </div>
                <div class="kpi-item">
                    <h5>Subscription Renewal Rate (%)</h5>
                    <p>Renewal percentage tracking</p>
                </div>
                <div class="kpi-item">
                    <h5>Average Appointments per Provider</h5>
                    <p>Provider performance metric</p>
                </div>
                <div class="kpi-item">
                    <h5>All Appointments</h5>
                    <p>Total appointment count</p>
                </div>
                <div class="kpi-item">
                    <h5>Pending Appointments</h5>
                    <p>Awaiting confirmation</p>
                </div>
                <div class="kpi-item">
                    <h5>Completed Appointments</h5>
                    <p>Successfully finished appointments</p>
                </div>
            </div>

            <div class="feature-box">
                <h4>🏆 Top Service Providers</h4>
                <p>A list of Top Service Providers is shown on the Dashboard, including their respective logos.</p>
            </div>

            <div class="feature-box">
                <h4>🆕 Recently Registered Service Providers List</h4>
                <p>The admin can view newly registered providers and update their status (Accepted/Rejected) directly from the list.</p>
            </div>

            <div class="feature-box">
                <h4>📅 Payments Calendar</h4>
                <p>Displays all upcoming payment dates along with the corresponding payment details.</p>
            </div>

            <div class="table-columns">
                <h5>Recent Subscriptions Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">Service Provider Name</div>
                    <div class="column-item">Email</div>
                    <div class="column-item">Phone</div>
                    <div class="column-item">Package Type</div>
                    <div class="column-item">Beauty Center Type</div>
                    <div class="column-item">Subscription Amount</div>
                    <div class="column-item">Fatoora (Invoice)</div>
                </div>
            </div>
        </div>

        <div id="subscribers" class="section">
            <h2><span class="icon">👥</span>Subscribers Management</h2>
            <p>The Admin has the ability to add new subscribers through a dedicated option, allowing the addition of free subscribers.</p>

            <div class="feature-box">
                <h4>➕ Add New Subscriber</h4>
                <p>Three free packages are available when adding a subscriber:</p>
                <ul>
                    <li><span class="highlight">LIINK Free Monthly</span></li>
                    <li><span class="highlight">LIINK Free Yearly</span></li>
                    <li><span class="highlight">LIINK Free Unlimited</span></li>
                </ul>
            </div>

            <div class="table-columns">
                <h5>Subscribers Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Name</div>
                    <div class="column-item">Subscription Plan</div>
                    <div class="column-item">Subscription Price</div>
                    <div class="column-item">Amount Captured</div>
                    <div class="column-item">VAT Certification</div>
                    <div class="column-item">VAT Certification Expiry Date</div>
                    <div class="column-item">Trade Certification</div>
                    <div class="column-item">Trade Certification Expiry Date</div>
                    <div class="column-item">Status</div>
                    <div class="column-item">Fatoora E-Invoice</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="feature-box">
                <h4>👁️ Actions Available</h4>
                <p>In the Actions column, there is a "View" (eye icon) button that allows the admin to:</p>
                <ul>
                    <li><strong>View Subscription Details:</strong> Displays a detailed view of all paid subscriptions by the subscriber</li>
                    <li><strong>Change Subscription:</strong> The admin can modify the subscriber's current subscription plan</li>
                </ul>
            </div>

            <div class="feature-box">
                <h4>📊 Export Options</h4>
                <p>Subscriber data can be exported in <span class="highlight">Excel</span> and <span class="highlight">PDF</span> formats, containing complete subscriber details.</p>
            </div>
        </div>

        <div id="customers" class="section">
            <h2><span class="icon">🛍️</span>Customers Management</h2>

            <div class="kpi-grid">
                <div class="kpi-item">
                    <h5>Total Customers</h5>
                    <p>Total number of registered customers</p>
                </div>
                <div class="kpi-item">
                    <h5>Total Earnings</h5>
                    <p>Total revenue from customer appointments</p>
                </div>
            </div>

            <div class="table-columns">
                <h5>Customer Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">Name</div>
                    <div class="column-item">Email</div>
                    <div class="column-item">Phone</div>
                    <div class="column-item">Total Appointments</div>
                    <div class="column-item">Total Paid Amount</div>
                    <div class="column-item">Client Birthday</div>
                    <div class="column-item">Status</div>
                    <div class="column-item">Action</div>
                </div>
            </div>

            <div class="feature-box">
                <h4>🔧 Admin Actions</h4>
                <ul>
                    <li><strong>Ban/Unban Customers:</strong> Via the Actions column</li>
                    <li><strong>View Basic Customer Details:</strong> Complete customer information</li>
                </ul>
            </div>
        </div>

        <div id="products" class="section">
            <h2><span class="icon">📦</span>Product Flow</h2>
            <p>The Product Flow section provides insights into product and beauty center performance.</p>

            <div class="feature-box">
                <h4>🔥 Popular Products</h4>
                <p>Displays the <span class="highlight">Top 3</span> most sold products across all shops, based on sales volume.</p>
            </div>

            <div class="feature-box">
                <h4>🏆 Popular Beauty Centers</h4>
                <p>Highlights the <span class="highlight">Top 3</span> beauty centers, including:</p>
                <ul>
                    <li>Number of Branches</li>
                    <li>Total Services Offered</li>
                    <li>Customer Count</li>
                </ul>
            </div>

            <div class="table-columns">
                <h5>Beauty Centers Listing Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Image</div>
                    <div class="column-item">Beauty Center Name</div>
                    <div class="column-item">Branch Count</div>
                    <div class="column-item">Services Count</div>
                    <div class="column-item">Customer Count</div>
                </div>
            </div>

            <div class="feature-box">
                <h4>🔍 Beauty Center Detailed View</h4>
                <p>Clicking on any entry opens detailed information including:</p>
                <ul>
                    <li>Name, Image, Branches</li>
                    <li>Cashiers, Employees, Services</li>
                    <li>Customers, Subscribed On</li>
                    <li>Current Subscription Plan</li>
                </ul>
            </div>

            <div class="feature-box">
                <h4>📊 Most Used Products</h4>
                <p>Shows the <span class="highlight">Top 3</span> most frequently used products for the selected beauty center.</p>
            </div>

            <div class="feature-box">
                <h4>📈 Category Breakdown</h4>
                <p>Filtering and analysis based on:</p>
                <ul>
                    <li>Selected Categories</li>
                    <li>Selected Branches</li>
                    <li>Custom Date Range</li>
                </ul>
                <p>Includes a <span class="highlight">Clustered Vertical Bar Graph</span> for comparative data visualization.</p>
            </div>

            <div class="table-columns">
                <h5>Products Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">Product Name</div>
                    <div class="column-item">Number of Units Sold</div>
                    <div class="column-item">Branch</div>
                    <div class="column-item">Category</div>
                    <div class="column-item">Customer Count</div>
                </div>
            </div>

            <div class="table-columns">
                <h5>Branches Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">Image</div>
                    <div class="column-item">Branch Name</div>
                    <div class="column-item">Address</div>
                    <div class="column-item">Date Joined</div>
                    <div class="column-item">Customer Count</div>
                </div>
            </div>
        </div>

        <div id="subscriptions" class="section">
            <h2><span class="icon">💳</span>Subscription Plan Management</h2>
            <p>This section allows the admin to manage all available subscription plans.</p>

            <div class="table-columns">
                <h5>Subscription Plans Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Package Type</div>
                    <div class="column-item">Name</div>
                    <div class="column-item">Price</div>
                    <div class="column-item">Status</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Edit Subscription Plan Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Package Type (Basic/Premium/Enterprise)</div>
                    <div class="field-item">Name</div>
                    <div class="field-item">Price</div>
                    <div class="field-item">VAT Price</div>
                    <div class="field-item">Details</div>
                    <div class="field-item">Description</div>
                </div>
            </div>
        </div>

        <div id="addons" class="section">
            <h2><span class="icon">⭐</span>Premium Add-ons</h2>
            <p>This module allows the admin to manage premium add-ons, which extend the features available in base subscription plans.</p>

            <div class="feature-box">
                <h4>👨‍💼 Cashier Add-on</h4>
                <ul>
                    <li>Base subscription includes <span class="highlight">1 cashier</span></li>
                    <li>Salons requiring more can purchase additional cashier licenses via premium add-ons</li>
                </ul>
            </div>

            <div class="feature-box">
                <h4>👥 Employee Add-on</h4>
                <ul>
                    <li>Base subscription includes <span class="highlight">4 employees</span></li>
                    <li>Additional employee slots can be added via premium add-ons</li>
                </ul>
            </div>

            <div class="form-fields">
                <h5>Add Premium Add-on Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Type (Cashier/Employee)</div>
                    <div class="field-item">Title</div>
                    <div class="field-item">Price</div>
                    <div class="field-item">Description</div>
                </div>
            </div>

            <h3>Premium Add-on Subscribers</h3>
            <p>This section displays a list of salons that have purchased premium add-ons.</p>

            <div class="table-columns">
                <h5>Premium Add-on Subscriber Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Primary Branch</div>
                    <div class="column-item">Premium Add-on</div>
                    <div class="column-item">Type</div>
                    <div class="column-item">Premium Add-on Price</div>
                    <div class="column-item">Amount Captured</div>
                    <div class="column-item">No. of Users</div>
                    <div class="column-item">Fatoora E-Invoice</div>
                </div>
            </div>
        </div>

        <div id="categories" class="section">
            <h2><span class="icon">🏷️</span>Category Management</h2>
            <p>The Admin can create and manage categories for both services and products.</p>

            <div class="table-columns">
                <h5>Category Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Picture</div>
                    <div class="column-item">Name</div>
                    <div class="column-item">Add Category</div>
                    <div class="column-item">Center</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add/Edit Category Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Category Name</div>
                    <div class="field-item">Category Description</div>
                    <div class="field-item">Category Picture</div>
                </div>
            </div>
        </div>

        <div id="amenities" class="section">
            <h2><span class="icon">🏨</span>Amenities Management</h2>
            <p>Admins can define amenities that salons can associate with their profiles.</p>

            <div class="table-columns">
                <h5>Amenities Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Picture</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Description</div>
                    <div class="column-item">Center</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Amenity Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                    <div class="field-item">Description</div>
                    <div class="field-item">Image</div>
                </div>
            </div>

            <h3>Center Amenities</h3>
            <p>Displays amenities subscribed or selected by specific beauty centers.</p>

            <div class="table-columns">
                <h5>Center Amenities Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Center</div>
                    <div class="column-item">Picture</div>
                    <div class="column-item">Amenity</div>
                </div>
            </div>
        </div>

        <div id="ads" class="section">
            <h2><span class="icon">📢</span>Ads Management</h2>
            <p>Admins can create advertisements that are shown on the customer dashboards.</p>

            <div class="table-columns">
                <h5>Ads Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Description</div>
                    <div class="column-item">LIINK</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add New Ad Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                    <div class="field-item">Description</div>
                    <div class="field-item">LIINK</div>
                    <div class="field-item">Picture</div>
                </div>
            </div>
        </div>

        <div id="blogs" class="section">
            <h2><span class="icon">📝</span>Blog Management</h2>
            <p>Admins can create blogs that appear on the Liink public website.</p>

            <div class="table-columns">
                <h5>Blogs Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Description</div>
                    <div class="column-item">Date</div>
                    <div class="column-item">Status</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Blog Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                    <div class="field-item">Description</div>
                    <div class="field-item">Facebook Link</div>
                    <div class="field-item">Instagram Link</div>
                    <div class="field-item">Twitter Link</div>
                    <div class="field-item">TikTok Link</div>
                    <div class="field-item">YouTube Link</div>
                    <div class="field-item">Picture</div>
                </div>
            </div>
        </div>

        <div id="support" class="section">
            <h2><span class="icon">🎧</span>Support Management</h2>

            <h3>Support Categories</h3>
            <p>Admins can create support ticket categories, enabling salons to raise tickets by category (e.g., billing, technical, etc.).</p>

            <div class="table-columns">
                <h5>Support Category Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Support Category Form Field:</h5>
                <div class="field-list">
                    <div class="field-item">Name</div>
                </div>
            </div>

            <h3>LIINK Support Tickets</h3>
            <p>Displays all support tickets raised by salons and service providers.</p>

            <div class="table-columns">
                <h5>Support Tickets Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">Ticket ID</div>
                    <div class="column-item">Support Category</div>
                    <div class="column-item">Beauty Center Name</div>
                    <div class="column-item">Description</div>
                    <div class="column-item">Status</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="feature-box">
                <h4>🔧 Ticket Actions</h4>
                <ul>
                    <li>View Contact Details</li>
                    <li>Delete</li>
                    <li>Mark as Complete</li>
                    <li>Mark as In Progress</li>
                </ul>
            </div>
        </div>

        <div id="expenses" class="section">
            <h2><span class="icon">💰</span>Expense Categories</h2>
            <p>Admins can create and manage expense categories, which shops/centers can use when logging expenses.</p>

            <div class="table-columns">
                <h5>Expense Category Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Name</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Expense Category Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                    <div class="field-item">Color</div>
                </div>
            </div>
        </div>

        <div id="brands" class="section">
            <h2><span class="icon">🏪</span>Product Brands</h2>
            <p>Admins can create product brands, which shops can associate with their products during product creation.</p>

            <div class="table-columns">
                <h5>Product Brand Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Name</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Product Brand Form Field:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                </div>
            </div>
        </div>

        <div id="website" class="section">
            <h2><span class="icon">🌐</span>Website Management</h2>

            <h3>Guide Section</h3>
            <p>Content shown under the "Guide" section on the public website.</p>

            <div class="table-columns">
                <h5>Guide Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Description</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <h3>Our Services</h3>
            <p>Services created in this section appear on the public-facing "Our Services" section of the website.</p>

            <div class="table-columns">
                <h5>Our Services Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Description</div>
                    <div class="column-item">Picture</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Service Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                    <div class="field-item">Description</div>
                    <div class="field-item">Image</div>
                </div>
            </div>

            <h3>Pages</h3>
            <p>This module allows admins to manage static web pages such as About Us, Privacy Policy, etc.</p>

            <div class="table-columns">
                <h5>Pages Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Description</div>
                    <div class="column-item">Page</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Edit Page Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                    <div class="field-item">Description</div>
                    <div class="field-item">Small Content</div>
                    <div class="field-item">Button Text</div>
                    <div class="field-item">URL</div>
                </div>
            </div>

            <h3>Testimonials</h3>
            <p>Testimonials submitted or created by the admin appear on the website's testimonial section.</p>

            <div class="table-columns">
                <h5>Testimonials Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Image</div>
                    <div class="column-item">Name</div>
                    <div class="column-item">Review</div>
                    <div class="column-item">Ratings</div>
                    <div class="column-item">Status</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Testimonial Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Name</div>
                    <div class="field-item">Review</div>
                    <div class="field-item">Ratings</div>
                    <div class="field-item">Picture</div>
                    <div class="field-item">Status</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>Contact Submissions</h3>
            <p>This section captures contact form submissions made via the website.</p>

            <div class="table-columns">
                <h5>Contact Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">First Name</div>
                    <div class="column-item">Last Name</div>
                    <div class="column-item">Phone</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="feature-box">
                <h4>👁️ View Action</h4>
                <p>View action allows the admin to see complete message details and respond as necessary.</p>
            </div>

            <h3>Newsletter Subscribers</h3>
            <p>Displays emails of users who subscribed to the newsletter from the website.</p>

            <div class="table-columns">
                <h5>Newsletter Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Email</div>
                </div>
            </div>

            <h3>Picture Management</h3>
            <p>Admins can manage images shown on the public website UI.</p>

            <div class="table-columns">
                <h5>Picture Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Picture</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <h3>Help Center</h3>
            <p>The Help Center allows admins to provide video tutorials or guidance links for salon owners and staff to learn how to manage the platform.</p>

            <div class="table-columns">
                <h5>Help Center Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">S.No.</div>
                    <div class="column-item">Title</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="form-fields">
                <h5>Add Help Center Form Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Title</div>
                    <div class="field-item">Video URL</div>
                </div>
            </div>
        </div>

        <div id="settings" class="section">
            <h2><span class="icon">⚙️</span>Account Settings</h2>
            <p>Admins can update their profile and business settings, including VAT details and branding.</p>

            <div class="form-fields">
                <h5>Account Setting Fields:</h5>
                <div class="field-list">
                    <div class="field-item">Name</div>
                    <div class="field-item">Email</div>
                    <div class="field-item">Password / Confirm Password</div>
                    <div class="field-item">VAT %</div>
                    <div class="field-item">Date of Birth</div>
                    <div class="field-item">Profile Picture</div>
                    <div class="field-item">Invoice Logo</div>
                    <div class="field-item">Invoice Name</div>
                    <div class="field-item">VAT Number</div>
                    <div class="field-item">Bio</div>
                    <div class="field-item">Facebook Link</div>
                    <div class="field-item">Instagram Link</div>
                    <div class="field-item">X Link</div>
                    <div class="field-item">YouTube Link</div>
                    <div class="field-item">Gender</div>
                    <div class="field-item">Country</div>
                    <div class="field-item">State</div>
                    <div class="field-item">City</div>
                    <div class="field-item">Address</div>
                    <div class="field-item">Postal ZIP Code</div>
                    <div class="field-item">Phone</div>
                </div>
            </div>

            <div class="note">
                <strong>Note:</strong> All updates are saved and reflected across system communications and invoice templates.
            </div>

            <h3>Language Translation System</h3>
            <p>This module supports multilingual translation (e.g., English & Arabic) across the platform.</p>

            <div class="table-columns">
                <h5>Language Table Columns:</h5>
                <div class="column-list">
                    <div class="column-item">Key</div>
                    <div class="column-item">Value</div>
                    <div class="column-item">Actions</div>
                </div>
            </div>

            <div class="feature-box">
                <h4>🌍 Localization</h4>
                <p>All frontend and user-visible texts are managed here, ensuring full localization support.</p>
            </div>

            <h3>Notification System</h3>
            <p>The platform triggers automated notifications to the Admin for major events.</p>

            <div class="feature-box">
                <h4>🔔 Trigger Events That Generate Notifications:</h4>
                <ul>
                    <li>Salon Registered</li>
                    <li>Salon Took a Subscription</li>
                    <li>Salon Updated Their Subscription</li>
                    <li>Guest Filled Contact Us Form</li>
                    <li>Salon Purchased Add-ons</li>
                    <li>Salon Submitted Support Ticket</li>
                    <li>Salon's Trade License Expired</li>
                    <li>Salon's VAT Certification Expired</li>
                    <li>Change in Salon Account Status</li>
                </ul>
            </div>
        </div>

        <div class="section" style="text-align: center; background: linear-gradient(135deg, #7c486c 0%, #7c486c 100%); color: white;">
            <h2 style="color: white;">🎯 Complete Admin Journey</h2>
            <p>This documentation covers the complete journey from admin login to logout, including every button and feature available in the admin platform. All features are explained from a business perspective to ensure comprehensive understanding of the LIINK Admin Panel capabilities.</p>
        </div>
    </div>
</body>
</html>
