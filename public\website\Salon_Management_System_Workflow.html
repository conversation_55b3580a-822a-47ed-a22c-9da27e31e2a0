<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIINK - Complete Beauty Center Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            border-radius: 15px;
            margin: -20px -20px 40px -20px;
        }
        
        .logo {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.4em;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .tagline {
            font-size: 1.1em;
            margin-top: 10px;
            opacity: 0.8;
            font-style: italic;
        }
        
        h1 {
            color: #7c486c;
            font-size: 2.8em;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #401B1B;
            font-size: 2em;
            margin-bottom: 25px;
            margin-top: 45px;
            padding-bottom: 15px;
            border-bottom: 3px solid #FBEBEC;
            position: relative;
        }
        
        h2::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: #7c486c;
        }
        
        h3 {
            color: #7c486c;
            font-size: 1.4em;
            margin-bottom: 15px;
            margin-top: 25px;
            font-weight: 600;
        }
        
        h4 {
            color: #401B1B;
            font-size: 1.2em;
            margin-bottom: 12px;
            margin-top: 18px;
            font-weight: 500;
        }
        
        .section {
            margin-bottom: 45px;
            padding: 30px;
            background: #FBEBEC;
            border-radius: 15px;
            border-left: 6px solid #7c486c;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .step {
            margin-bottom: 30px;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
            border: 1px solid #ECD8BD;
        }
        
        .field-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 18px 0;
            border: 1px solid #e9ecef;
        }
        
        .field-item {
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
            color: #495057;
        }
        
        .field-item:last-child {
            border-bottom: none;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-top: 5px solid #7c486c;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .dashboard-card {
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-3px);
        }
        
        .dashboard-card h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 12px;
            font-weight: 600;
        }
        
        .dashboard-card p {
            opacity: 0.9;
            font-size: 1em;
            line-height: 1.4;
        }
        
        .highlight {
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .highlight h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.6em;
        }
        
        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .process-step {
            background: linear-gradient(135deg, #7c486c 0%, #401B1B 100%);
            color: white;
            padding: 18px 25px;
            border-radius: 30px;
            margin: 8px;
            font-weight: 600;
            text-align: center;
            flex: 1;
            min-width: 160px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .process-step:hover {
            transform: scale(1.05);
        }
        
        .process-arrow {
            font-size: 1.8em;
            color: #7c486c;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .stats-overview {
            background: linear-gradient(135deg, #ECD8BD 0%, #FBEBEC 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #7c486c;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .table-columns {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 18px 0;
            border-left: 5px solid #2196F3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }
        
        .menu-item {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 18px 0;
            border-left: 5px solid #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
        }
        
        .warning {
            background: #fff8e1;
            color: #e65100;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ff9800;
            margin: 18px 0;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #4caf50;
            margin: 18px 0;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
        }
        
        .info {
            background: #e3f2fd;
            color: #1565c0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196f3;
            margin: 18px 0;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header {
                margin: -15px -15px 30px -15px;
                padding: 30px 15px;
            }
            
            .logo {
                font-size: 2.5em;
            }
            
            h1 {
                font-size: 2.2em;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .process-flow {
                flex-direction: column;
            }
            
            .process-arrow {
                transform: rotate(90deg);
                margin: 15px 0;
            }
            
            .section {
                padding: 20px;
            }
            
            .step {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Complete Workflow Section -->
        <div class="section" id="workflow-section">
            <h2>🔄 Complete LIINK Beauty Center Management Workflow</h2>

            <div class="highlight">
                <h3>📊 Workflow Overview</h3>
                <p>This comprehensive workflow guides you through every step of setting up and operating your Beauty Center with LIINK - from initial registration to full business operations.</p>
            </div>

            <!-- Step 1: Getting Started -->
            <div class="step">
                <h3>🚀 Step 1: Getting Started - Registration Process</h3>

                <div class="process-flow">
                    <div class="process-step">Visit Homepage</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Choose Package</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Register</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Setup Center</div>
                </div>

                <div class="feature-grid">
                    <div class="dashboard-card">
                        <h4>🎯 Package Selection</h4>
                        <p><strong>Free Trial:</strong> Perfect for testing<br>
                        <strong>Monthly:</strong> Flexible billing<br>
                        <strong>Yearly:</strong> Best value savings</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📝 Registration Form</h4>
                        <p><strong>Basic Info:</strong> Shop details, VAT, Trade certificates<br>
                        <strong>Address:</strong> Complete location information</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>✅ Access Control</h4>
                        <p><strong>Free Trial:</strong> Immediate access<br>
                        <strong>Paid Plans:</strong> Admin approval required</p>
                    </div>
                </div>
            </div>

            <!-- Step 2: Center Settings -->
            <div class="step">
                <h3>⚙️ Step 2: Center Settings - Business Profile Setup</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📸 Visual Identity</h4>
                        <div class="field-item">• Logo Picture</div>
                        <div class="field-item">• Profile Picture</div>
                        <div class="field-item">• Reception Area Picture</div>
                        <div class="field-item">• Service Area Picture</div>
                    </div>
                    <div class="feature-card">
                        <h4>📍 Location & Contact</h4>
                        <div class="field-item">• Address & Google Maps</div>
                        <div class="field-item">• City & State</div>
                        <div class="field-item">• Contact Number</div>
                    </div>
                    <div class="feature-card">
                        <h4>📱 Social Media</h4>
                        <div class="field-item">• Snapchat</div>
                        <div class="field-item">• Instagram</div>
                        <div class="field-item">• X (Twitter)</div>
                        <div class="field-item">• WhatsApp</div>
                    </div>
                    <div class="feature-card">
                        <h4>⏰ Operations</h4>
                        <div class="field-item">• Opening & Closing Time</div>
                        <div class="field-item">• Slot Time (5-min buffer)</div>
                        <div class="field-item">• Appointment Type</div>
                        <div class="field-item">• Center Type</div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Services Setup -->
            <div class="step">
                <h3>💅 Step 3: Services & Categories Management</h3>

                <div class="process-flow">
                    <div class="process-step">Create Categories</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Add Services</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Set Pricing</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Assign Employees</div>
                </div>

                <div class="field-list">
                    <h4>Service Setup Requirements:</h4>
                    <div class="field-item">• Branch Selection</div>
                    <div class="field-item">• Category Assignment</div>
                    <div class="field-item">• Service Name & Price</div>
                    <div class="field-item">• Service Description</div>
                    <div class="field-item">• Service Picture</div>
                    <div class="field-item">• Employee Assignment</div>
                </div>
            </div>

            <!-- Step 4: Cashier Setup -->
            <div class="step">
                <h3>💰 Step 4: Cashier Management Setup</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>➕ Add Cashier Process</h4>
                        <div class="field-item">• Branch Selection</div>
                        <div class="field-item">• First & Last Name</div>
                        <div class="field-item">• Email & Password</div>
                        <div class="field-item">• Phone & Address</div>
                        <div class="field-item">• Date of Birth & Gender</div>
                        <div class="field-item">• Profile Picture</div>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Cashier Overview</h4>
                        <div class="field-item">• View all cashiers</div>
                        <div class="field-item">• Edit cashier details</div>
                        <div class="field-item">• Delete cashier records</div>
                        <div class="field-item">• Track performance</div>
                    </div>
                </div>
            </div>

            <!-- Step 5: Employee Setup -->
            <div class="step">
                <h3>👥 Step 5: Employee Management Setup</h3>

                <div class="process-flow">
                    <div class="process-step">Add Employee</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Upload Documents</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Assign Services</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Track Performance</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📝 Employee Details</h4>
                        <div class="field-item">• Branch & Type</div>
                        <div class="field-item">• Personal Information</div>
                        <div class="field-item">• Contact Details</div>
                        <div class="field-item">• Date of Birth & Gender</div>
                    </div>
                    <div class="feature-card">
                        <h4>📄 Documentation</h4>
                        <div class="field-item">• Employee ID</div>
                        <div class="field-item">• Insurance Card</div>
                        <div class="field-item">• Health Card</div>
                        <div class="field-item">• Contract Expiry</div>
                    </div>
                    <div class="feature-card">
                        <h4>💼 Work Assignment</h4>
                        <div class="field-item">• Service Categories</div>
                        <div class="field-item">• Specific Services</div>
                        <div class="field-item">• Performance Tracking</div>
                    </div>
                    <div class="feature-card">
                        <h4>📅 Leave Management</h4>
                        <div class="field-item">• Add Employee Leaves</div>
                        <div class="field-item">• Date Range Selection</div>
                        <div class="field-item">• Leave Description</div>
                    </div>
                </div>
            </div>

            <!-- Step 6: Amenities -->
            <div class="step">
                <h3>🏨 Step 6: Amenities Management</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🛋️ Add Amenities</h4>
                        <div class="field-item">• Select from available amenities</div>
                        <div class="field-item">• Upload amenity pictures</div>
                        <div class="field-item">• Manage amenity list</div>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Amenities Overview</h4>
                        <div class="field-item">• View all amenities</div>
                        <div class="field-item">• Delete amenities</div>
                        <div class="field-item">• Update amenity information</div>
                    </div>
                </div>
            </div>

            <!-- Step 7: Dashboard Operations -->
            <div class="step">
                <h3>📊 Step 7: Business Dashboard - Complete Overview</h3>

                <div class="feature-grid">
                    <div class="dashboard-card">
                        <h4>📈 Dashboard Analytics</h4>
                        <p>• Active Appointments<br>
                        • Upcoming Appointments<br>
                        • Total Customers<br>
                        • All Services & Products<br>
                        • Total Revenue</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📅 Appointments Calendar</h4>
                        <p>🟡 Pending | 🟢 Approved<br>
                        🔵 Complete | 🔴 Cancelled<br>
                        Visual calendar management</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📋 Performance Tables</h4>
                        <p>• Employee Performance<br>
                        • Assigned Clients<br>
                        • Revenue Tracking<br>
                        • Appointments Overview</p>
                    </div>
                </div>
            </div>

            <!-- Step 8: Appointments Management -->
            <div class="step">
                <h3>📅 Step 8: Appointments Management</h3>

                <div class="process-flow">
                    <div class="process-step">Customer Registration</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Service Selection</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Time Booking</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Confirmation</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>👤 Customer Types</h4>
                        <div class="field-item">• Walk-in Customers</div>
                        <div class="field-item">• Online Customers</div>
                        <div class="field-item">• Appointment Scheduling</div>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Appointment Tracking</h4>
                        <div class="field-item">• Customer Details</div>
                        <div class="field-item">• Service Information</div>
                        <div class="field-item">• Time & Date</div>
                        <div class="field-item">• Status Management</div>
                    </div>
                </div>
            </div>

            <!-- Step 9: Branch & Subscription Management -->
            <div class="step">
                <h3>🏢 Step 9: Branch & Subscription Management</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🏪 Multi-Branch Operations</h4>
                        <div class="field-item">• Add New Branch</div>
                        <div class="field-item">• Package Selection</div>
                        <div class="field-item">• Payment Completion</div>
                        <div class="field-item">• Branch Management</div>
                    </div>
                    <div class="feature-card">
                        <h4>📦 Subscription Management</h4>
                        <div class="field-item">• Price & Package Type</div>
                        <div class="field-item">• Payment Dates</div>
                        <div class="field-item">• Next Payment Date</div>
                        <div class="field-item">• Invoice Generation</div>
                    </div>
                </div>
            </div>

            <!-- Step 10: Inventory Management -->
            <div class="step">
                <h3>📦 Step 10: Complete Inventory Management</h3>

                <div class="process-flow">
                    <div class="process-step">Add Suppliers</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Manage Products</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Stock-In Process</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">FIFO Consumption</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🏭 Supplier Management</h4>
                        <div class="field-item">• Supplier Details</div>
                        <div class="field-item">• Contact Information</div>
                        <div class="field-item">• Category Assignment</div>
                        <div class="field-item">• Company Details</div>
                    </div>
                    <div class="feature-card">
                        <h4>📦 Product Management</h4>
                        <div class="field-item">• Consumables vs Equipment</div>
                        <div class="field-item">• Product Brands</div>
                        <div class="field-item">• Stock Thresholds</div>
                        <div class="field-item">• Product Categories</div>
                    </div>
                    <div class="feature-card">
                        <h4>📥 Stock-In Process</h4>
                        <div class="field-item">• Purchase Orders (PO)</div>
                        <div class="field-item">• Product Review</div>
                        <div class="field-item">• Threshold Alerts</div>
                        <div class="field-item">• Auto PO Creation</div>
                    </div>
                    <div class="feature-card">
                        <h4>📤 Stock Consumption</h4>
                        <div class="field-item">🔄 FIFO Logic</div>
                        <div class="field-item">• Service (Manual)</div>
                        <div class="field-item">• Sale (Auto/Manual)</div>
                        <div class="field-item">• Expiry (Auto)</div>
                        <div class="field-item">• Retain (Refund)</div>
                    </div>
                </div>
            </div>

            <!-- Step 11: Financial Management -->
            <div class="step">
                <h3>💰 Step 11: Complete Financial Management</h3>

                <div class="process-flow">
                    <div class="process-step">Expense Tracking</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Revenue Management</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Financial Analytics</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Reports Generation</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>💸 Expense Management</h4>
                        <div class="field-item">• PO → Cost → Expense</div>
                        <div class="field-item">• Expiry Costs</div>
                        <div class="field-item">• Manual Expenses</div>
                        <div class="field-item">• Category-wise tracking</div>
                    </div>
                    <div class="feature-card">
                        <h4>📈 Revenue Management</h4>
                        <div class="field-item">• Sales Revenue</div>
                        <div class="field-item">• Service Revenue</div>
                        <div class="field-item">• Combined Invoices</div>
                        <div class="field-item">• Manual Revenue</div>
                        <div class="field-item">• B2B Refunds</div>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Financial Analytics</h4>
                        <div class="field-item">• Income Statement</div>
                        <div class="field-item">• Employee Statement</div>
                        <div class="field-item">• Depreciation</div>
                        <div class="field-item">• Amortization</div>
                    </div>
                    <div class="feature-card">
                        <h4>📋 Saudi Compliance</h4>
                        <div class="field-item">• Government ready reports</div>
                        <div class="field-item">• Exportable statements</div>
                        <div class="field-item">• External audit support</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Workflow Steps -->
        <div class="section">
            <h2>🔄 Advanced Workflow Steps</h2>

            <!-- Step 12: Customer Evaluations -->
            <div class="step">
                <h3>⭐ Step 12: Customer Evaluations Setup</h3>

                <div class="process-flow">
                    <div class="process-step">Create Questions</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Configure Ratings</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Send Feedback Links</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Track Reviews</div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📝 Evaluation Questions</h4>
                        <div class="field-item">• Custom evaluation questions</div>
                        <div class="field-item">• Rating scales (1-5)</div>
                        <div class="field-item">• Text feedback questions</div>
                        <div class="field-item">• Preview evaluation forms</div>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Customer Records</h4>
                        <div class="field-item">• Customer details tracking</div>
                        <div class="field-item">• Employee assignment</div>
                        <div class="field-item">• Ratings & reviews</div>
                        <div class="field-item">• Email feedback links</div>
                    </div>
                </div>
            </div>

            <!-- Step 13: Discount Management -->
            <div class="step">
                <h3>🎯 Step 13: Discount Management</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>💰 Discount Creation</h4>
                        <div class="field-item">• Percentage-based discounts</div>
                        <div class="field-item">• Fixed amount discounts</div>
                        <div class="field-item">• Validity periods</div>
                        <div class="field-item">• Appointment application</div>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Discount Tracking</h4>
                        <div class="field-item">• Revenue impact analysis</div>
                        <div class="field-item">• Customer response tracking</div>
                        <div class="field-item">• Usage analytics</div>
                        <div class="field-item">• Remaining quantity</div>
                    </div>
                </div>
            </div>

            <!-- Step 14: LIINK Support -->
            <div class="step">
                <h3>🆘 Step 14: LIINK Support System</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📚 Help Centre Access</h4>
                        <div class="field-item">• Video tutorials</div>
                        <div class="field-item">• Written guides</div>
                        <div class="field-item">• Feature-specific help</div>
                        <div class="field-item">• Search functionality</div>
                        <div class="field-item">• Best practices & tips</div>
                    </div>
                    <div class="feature-card">
                        <h4>🎫 Support Tickets</h4>
                        <div class="field-item">• Submit technical requests</div>
                        <div class="field-item">• Track ticket status</div>
                        <div class="field-item">• Priority levels</div>
                        <div class="field-item">• Multiple support channels</div>
                    </div>
                </div>
            </div>

            <!-- Step 15: System Management -->
            <div class="step">
                <h3>👥 Step 15: System Management & Settings</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>👤 Customer Management</h4>
                        <div class="field-item">• Walk-in customers</div>
                        <div class="field-item">• Online customers</div>
                        <div class="field-item">• Customer profiles</div>
                    </div>
                    <div class="feature-card">
                        <h4>📅 Off Dates Management</h4>
                        <div class="field-item">• Beauty Center closure dates</div>
                        <div class="field-item">• Holiday scheduling</div>
                        <div class="field-item">• Maintenance days</div>
                        <div class="field-item">• Special event closures</div>
                    </div>
                    <div class="feature-card">
                        <h4>📦 Subscription Management</h4>
                        <div class="field-item">• View current plan</div>
                        <div class="field-item">• Upgrade/downgrade</div>
                        <div class="field-item">• Renewal tracking</div>
                        <div class="field-item">• Payment methods</div>
                    </div>
                    <div class="feature-card">
                        <h4>⭐ Premium Addons</h4>
                        <div class="field-item">• View purchased addons</div>
                        <div class="field-item">• Activate/deactivate features</div>
                        <div class="field-item">• Usage tracking</div>
                        <div class="field-item">• Purchase additional addons</div>
                    </div>
                </div>
            </div>

            <!-- Step 16: Additional Features -->
            <div class="step">
                <h3>🔔 Step 16: Additional System Features</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📬 Notifications & Alerts</h4>
                        <div class="field-item">• Appointment notifications</div>
                        <div class="field-item">• Payment reminders</div>
                        <div class="field-item">• Stock alerts</div>
                        <div class="field-item">• System updates</div>
                        <div class="field-item">• Employee notifications</div>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Analytics Dashboard</h4>
                        <div class="field-item">• Stock analytics</div>
                        <div class="field-item">• Profit calculations</div>
                        <div class="field-item">• Real-time updates</div>
                        <div class="field-item">• Revenue analysis</div>
                        <div class="field-item">• Net margin percentage</div>
                    </div>
                </div>
            </div>

            <!-- Final Step: Complete Operation -->
            <div class="step">
                <h3>✅ Step 17: Complete System Operation</h3>

                <div class="highlight">
                    <h3>🎉 Congratulations! Your Beauty Center is Now Fully Operational</h3>
                    <p>You have successfully completed the entire LIINK setup workflow. Your Beauty Center management system is now ready for full business operations with all features activated and configured.</p>
                </div>

                <div class="stats-overview">
                    <h3 style="color: #7c486c; margin-top: 0;">📈 What You've Accomplished</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">✅</div>
                            <div class="stat-label">Registration Complete</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">✅</div>
                            <div class="stat-label">Center Settings Configured</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">✅</div>
                            <div class="stat-label">Staff & Services Ready</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">✅</div>
                            <div class="stat-label">Full Operations Active</div>
                        </div>
                    </div>
                </div>

                <div class="process-flow">
                    <div class="process-step">Start Taking Appointments</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Manage Daily Operations</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Track Performance</div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">Grow Your Business</div>
                </div>
            </div>
        </div>

        <!-- Complete Menu Structure -->
        <div class="section">
            <h2>📋 Complete System Menu Structure</h2>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🏪 Business Management</h4>
                    <div class="field-item">• Business Dashboard</div>
                    <div class="field-item">• Center Settings</div>
                    <div class="field-item">• Branches Management</div>
                    <div class="field-item">• Subscription Management</div>
                </div>

                <div class="feature-card">
                    <h4>👥 Staff Management</h4>
                    <div class="field-item">• Employee Management</div>
                    <div class="field-item">• Cashier Management</div>
                    <div class="field-item">• Employee Leaves</div>
                    <div class="field-item">• Performance Tracking</div>
                </div>

                <div class="feature-card">
                    <h4>🛍️ Services & Products</h4>
                    <div class="field-item">• Categories & Services</div>
                    <div class="field-item">• Product Management</div>
                    <div class="field-item">• Stock Management</div>
                    <div class="field-item">• Amenities</div>
                </div>

                <div class="feature-card">
                    <h4>📅 Operations</h4>
                    <div class="field-item">• Appointments Management</div>
                    <div class="field-item">• Customer Management</div>
                    <div class="field-item">• Calendar View</div>
                    <div class="field-item">• Off Dates Management</div>
                </div>

                <div class="feature-card">
                    <h4>📦 Inventory</h4>
                    <div class="field-item">• Supplier Management</div>
                    <div class="field-item">• Stock In/Out</div>
                    <div class="field-item">• Product Threshold</div>
                    <div class="field-item">• FIFO Management</div>
                </div>

                <div class="feature-card">
                    <h4>💰 Financial</h4>
                    <div class="field-item">• Revenue Management</div>
                    <div class="field-item">• Expense Tracking</div>
                    <div class="field-item">• Financial Reports</div>
                    <div class="field-item">• Analytics</div>
                </div>

                <div class="feature-card">
                    <h4>📊 Reports & Analytics</h4>
                    <div class="field-item">• Income Statements</div>
                    <div class="field-item">• Performance Reports</div>
                    <div class="field-item">• Stock Analytics</div>
                    <div class="field-item">• Export Functions</div>
                </div>

                <div class="feature-card">
                    <h4>⚙️ System</h4>
                    <div class="field-item">• Help Center</div>
                    <div class="field-item">• Notifications</div>
                    <div class="field-item">• Settings</div>
                    <div class="field-item">• Support</div>
                </div>
            </div>
        </div>

    </div>
</body>
</html>
